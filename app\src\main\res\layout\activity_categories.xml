<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:ads="http://schemas.android.com/apk/res-auto"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/modern_background"
    android:layoutDirection="locale"
    tools:context=".Categories">

    <!-- Compact Header Section -->
    <androidx.cardview.widget.CardView
        android:id="@+id/header_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="12dp"
        android:minHeight="160dp"
        app:cardCornerRadius="20dp"
        app:cardElevation="8dp"
        app:cardBackgroundColor="@android:color/transparent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="160dp"
            android:background="@drawable/modern_gradient_background"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="16dp">

            <!-- App Logo - Compact -->
            <androidx.cardview.widget.CardView
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="30dp"
                app:cardElevation="8dp"
                app:cardBackgroundColor="@android:color/white">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="12dp"
                    android:src="@drawable/small_logo"
                    android:scaleType="centerInside" />

            </androidx.cardview.widget.CardView>

            <!-- App Title - Compact -->
            <TextView
                android:id="@+id/app_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/app_name"
                android:textColor="@color/text_on_primary"
                android:textSize="20sp"
                android:textStyle="bold"
                android:fontFamily="@font/blabeloo"
                android:layout_marginBottom="6dp"
                android:gravity="center"
                android:maxLines="1"
                android:ellipsize="end"
                android:autoSizeTextType="uniform"
                android:autoSizeMinTextSize="16sp"
                android:autoSizeMaxTextSize="22sp"
                android:autoSizeStepGranularity="2sp" />

            <!-- Subtitle - Compact -->
            <TextView
                android:id="@+id/app_subtitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/choose_category"
                android:textColor="@color/text_on_primary"
                android:textSize="12sp"
                android:fontFamily="@font/blabeloo"
                android:alpha="0.9"
                android:gravity="center"
                android:maxLines="1"
                android:ellipsize="end"
                android:autoSizeTextType="uniform"
                android:autoSizeMinTextSize="10sp"
                android:autoSizeMaxTextSize="14sp"
                android:autoSizeStepGranularity="1sp" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- Categories Grid - Properly spaced -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="150dp"
        android:layout_marginBottom="170dp"
        android:fillViewport="true"
        android:paddingBottom="16dp"
        android:clipToPadding="false"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Row 1: Flowers & Cartoons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <!-- Flowers Card -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:orientation="vertical"
                    android:gravity="center">

                    <!-- Card Image -->
                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="160dp"
                        android:foreground="@drawable/beautiful_ripple_effect"
                        android:clickable="true"
                        android:focusable="true"
                        app:cardCornerRadius="20dp"
                        app:cardElevation="12dp"
                        app:cardUseCompatPadding="true"
                        app:cardBackgroundColor="@android:color/white">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent">

                            <!-- Clear Image -->
                            <ImageView
                                android:id="@+id/c_1"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop"
                                android:src="@drawable/gp1_press" />

                            <!-- Light Overlay for clarity -->
                            <View
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@drawable/light_overlay"
                                android:alpha="0.1" />

                            <!-- Category Icon -->
                            <androidx.cardview.widget.CardView
                                android:layout_width="50dp"
                                android:layout_height="50dp"
                                android:layout_alignParentTop="true"
                                android:layout_alignParentEnd="true"
                                android:layout_margin="12dp"
                                app:cardCornerRadius="25dp"
                                app:cardElevation="8dp"
                                app:cardBackgroundColor="@color/flowers_color">

                                <ImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_margin="10dp"
                                    android:src="@drawable/ic_flowers_modern"
                                    android:tint="@android:color/white" />

                            </androidx.cardview.widget.CardView>

                        </RelativeLayout>

                    </androidx.cardview.widget.CardView>

                    <!-- Title Section Below Image -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:background="@drawable/beautiful_title_background"
                        android:orientation="vertical"
                        android:padding="16dp"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/text_card_name1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/flowers"
                            android:textColor="@color/text_primary"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:fontFamily="@font/blabeloo"
                            android:letterSpacing="0.02" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="@string/flowers_desc"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                </LinearLayout>

                <!-- Cartoons Card -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:orientation="vertical"
                    android:gravity="center">

                    <!-- Card Image -->
                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="160dp"
                        android:foreground="@drawable/beautiful_ripple_effect"
                        android:clickable="true"
                        android:focusable="true"
                        app:cardCornerRadius="20dp"
                        app:cardElevation="12dp"
                        app:cardUseCompatPadding="true"
                        app:cardBackgroundColor="@android:color/white">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent">

                            <!-- Clear Image -->
                            <ImageView
                                android:id="@+id/c_2"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop"
                                android:src="@drawable/gp2_press" />

                            <!-- Light Overlay for clarity -->
                            <View
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@drawable/light_overlay"
                                android:alpha="0.1" />

                            <!-- Category Icon -->
                            <androidx.cardview.widget.CardView
                                android:layout_width="50dp"
                                android:layout_height="50dp"
                                android:layout_alignParentTop="true"
                                android:layout_alignParentEnd="true"
                                android:layout_margin="12dp"
                                app:cardCornerRadius="25dp"
                                app:cardElevation="8dp"
                                app:cardBackgroundColor="@color/cartoons_color">

                                <ImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_margin="10dp"
                                    android:src="@drawable/ic_cartoons_modern"
                                    android:tint="@android:color/white" />

                            </androidx.cardview.widget.CardView>

                        </RelativeLayout>

                    </androidx.cardview.widget.CardView>

                    <!-- Title Section Below Image -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:background="@drawable/beautiful_title_background"
                        android:orientation="vertical"
                        android:padding="16dp"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/text_card_name2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/cartoons"
                            android:textColor="@color/text_primary"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:fontFamily="@font/blabeloo"
                            android:letterSpacing="0.02" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="@string/cartoons_desc"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>



            <!-- Row 2: Animals & Foods -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <!-- Animals Card -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:orientation="vertical"
                    android:gravity="center">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="160dp"
                        android:foreground="@drawable/beautiful_ripple_effect"
                        android:clickable="true"
                        android:focusable="true"
                        app:cardCornerRadius="20dp"
                        app:cardElevation="12dp"
                        app:cardUseCompatPadding="true"
                        app:cardBackgroundColor="@android:color/white">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent">

                            <ImageView
                                android:id="@+id/c_3"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop"
                                android:src="@drawable/gp3_press" />

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@drawable/light_overlay"
                                android:alpha="0.1" />

                            <androidx.cardview.widget.CardView
                                android:layout_width="50dp"
                                android:layout_height="50dp"
                                android:layout_alignParentTop="true"
                                android:layout_alignParentEnd="true"
                                android:layout_margin="12dp"
                                app:cardCornerRadius="25dp"
                                app:cardElevation="8dp"
                                app:cardBackgroundColor="@color/animals_color">

                                <ImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_margin="10dp"
                                    android:src="@drawable/ic_animals_modern"
                                    android:tint="@android:color/white" />

                            </androidx.cardview.widget.CardView>

                        </RelativeLayout>

                    </androidx.cardview.widget.CardView>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:background="@drawable/beautiful_title_background"
                        android:orientation="vertical"
                        android:padding="16dp"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/text_card_name3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/animals"
                            android:textColor="@color/text_primary"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:fontFamily="@font/blabeloo"
                            android:letterSpacing="0.02" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="@string/animals_desc"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                </LinearLayout>

                <!-- Foods Card -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:orientation="vertical"
                    android:gravity="center">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="160dp"
                        android:foreground="@drawable/beautiful_ripple_effect"
                        android:clickable="true"
                        android:focusable="true"
                        app:cardCornerRadius="20dp"
                        app:cardElevation="12dp"
                        app:cardUseCompatPadding="true"
                        app:cardBackgroundColor="@android:color/white">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent">

                            <ImageView
                                android:id="@+id/c_4"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop"
                                android:src="@drawable/gp4_press" />

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@drawable/light_overlay"
                                android:alpha="0.1" />

                            <androidx.cardview.widget.CardView
                                android:layout_width="50dp"
                                android:layout_height="50dp"
                                android:layout_alignParentTop="true"
                                android:layout_alignParentEnd="true"
                                android:layout_margin="12dp"
                                app:cardCornerRadius="25dp"
                                app:cardElevation="8dp"
                                app:cardBackgroundColor="@color/foods_color">

                                <ImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_margin="10dp"
                                    android:src="@drawable/ic_foods_modern"
                                    android:tint="@android:color/white" />

                            </androidx.cardview.widget.CardView>

                        </RelativeLayout>

                    </androidx.cardview.widget.CardView>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:background="@drawable/beautiful_title_background"
                        android:orientation="vertical"
                        android:padding="16dp"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/text_card_name4"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/foods"
                            android:textColor="@color/text_primary"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:fontFamily="@font/blabeloo"
                            android:letterSpacing="0.02" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="@string/foods_desc"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <!-- Row 3: Transport & Nature -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <!-- Transport Card -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:orientation="vertical"
                    android:gravity="center">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="160dp"
                        android:foreground="@drawable/beautiful_ripple_effect"
                        android:clickable="true"
                        android:focusable="true"
                        app:cardCornerRadius="20dp"
                        app:cardElevation="12dp"
                        app:cardUseCompatPadding="true"
                        app:cardBackgroundColor="@android:color/white">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent">

                            <ImageView
                                android:id="@+id/c_5"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop"
                                android:src="@drawable/gp5_press" />

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@drawable/light_overlay"
                                android:alpha="0.1" />

                            <androidx.cardview.widget.CardView
                                android:layout_width="50dp"
                                android:layout_height="50dp"
                                android:layout_alignParentTop="true"
                                android:layout_alignParentEnd="true"
                                android:layout_margin="12dp"
                                app:cardCornerRadius="25dp"
                                app:cardElevation="8dp"
                                app:cardBackgroundColor="@color/transport_color">

                                <ImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_margin="10dp"
                                    android:src="@drawable/ic_transport_modern"
                                    android:tint="@android:color/white" />

                            </androidx.cardview.widget.CardView>

                        </RelativeLayout>

                    </androidx.cardview.widget.CardView>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:background="@drawable/beautiful_title_background"
                        android:orientation="vertical"
                        android:padding="16dp"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/text_card_name5"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/transport"
                            android:textColor="@color/text_primary"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:fontFamily="@font/blabeloo"
                            android:letterSpacing="0.02" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="@string/transport_desc"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                </LinearLayout>

                <!-- Nature Card -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:orientation="vertical"
                    android:gravity="center">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="160dp"
                        android:foreground="@drawable/beautiful_ripple_effect"
                        android:clickable="true"
                        android:focusable="true"
                        app:cardCornerRadius="20dp"
                        app:cardElevation="12dp"
                        app:cardUseCompatPadding="true"
                        app:cardBackgroundColor="@android:color/white">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent">

                            <ImageView
                                android:id="@+id/c_6"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop"
                                android:src="@drawable/gp6_press" />

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@drawable/light_overlay"
                                android:alpha="0.1" />

                            <androidx.cardview.widget.CardView
                                android:layout_width="50dp"
                                android:layout_height="50dp"
                                android:layout_alignParentTop="true"
                                android:layout_alignParentEnd="true"
                                android:layout_margin="12dp"
                                app:cardCornerRadius="25dp"
                                app:cardElevation="8dp"
                                app:cardBackgroundColor="@color/nature_color">

                                <ImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_margin="10dp"
                                    android:src="@drawable/ic_nature_modern"
                                    android:tint="@android:color/white" />

                            </androidx.cardview.widget.CardView>

                        </RelativeLayout>

                    </androidx.cardview.widget.CardView>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:background="@drawable/beautiful_title_background"
                        android:orientation="vertical"
                        android:padding="16dp"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/text_card_name6"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/nature"
                            android:textColor="@color/text_primary"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:fontFamily="@font/blabeloo"
                            android:letterSpacing="0.02" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="@string/nature_desc"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>



        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Bottom Ad Banner - Optimized Design -->
    <com.google.android.gms.ads.AdView
        android:id="@+id/adView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_marginBottom="90dp"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp"
        android:layout_marginTop="8dp"
        ads:adSize="SMART_BANNER"
        ads:adUnitId="@string/banner_unit_id" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>


