package com.alwan.kids2025;

import android.content.Intent;
import android.content.SharedPreferences;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.AdapterView;
import android.widget.GridView;
import android.widget.Toast;

import androidx.activity.OnBackPressedCallback;
import androidx.annotation.NonNull;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.ads.mediation.admob.AdMobAdapter;
import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.RequestConfiguration;
import com.google.android.gms.ads.initialization.InitializationStatus;
import com.google.android.gms.ads.initialization.OnInitializationCompleteListener;
import com.google.android.gms.ads.interstitial.InterstitialAd;
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback;
import com.google.android.play.core.review.ReviewInfo;
import com.google.android.play.core.review.ReviewManager;
import com.google.android.play.core.review.ReviewManagerFactory;
import com.alwan.kids2025.managers.SimpleReviewManager;
import com.alwan.kids2025.managers.UpdateManager;
import com.alwan.kids2025.managers.AssetManager;

import java.util.ArrayList;

// import static com.alwan.kids2025.MainActivity.readyForReview;
import static com.google.android.gms.ads.RequestConfiguration.MAX_AD_CONTENT_RATING_G;
import static com.google.android.gms.ads.RequestConfiguration.TAG_FOR_CHILD_DIRECTED_TREATMENT_TRUE;

/**
 * Created by AHED.OMN on 15/5/2025.
 * <EMAIL>
 */
public class CategoryItems extends BaseActivity {
    int code;
    int imagesArray;
    String title;
    public static AdView mAdView;
    SharedPreferences preferences;
    int pos;
    private GridView gridView;
    private GridViewAdapter gridAdapter;
    private RecyclerView recyclerView;
    private ModernGridAdapter modernAdapter;
    private InterstitialAd mInterstitialAd;
    private static final String TAG = "AdMob ads consent";
    ReviewInfo reviewInfo;
    private SimpleReviewManager simpleReviewManager;
    private UpdateManager updateManager;
    private AssetManager assetManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_category_items);

        // --- استخدام آلية OnBackPressedDispatcher بدلاً من تجاوز onBackPressed() ---
        getOnBackPressedDispatcher().addCallback(this, new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                // عند الضغط على زر الرجوع، يتم الانتقال إلى شاشة Categories.
                Intent i = new Intent(CategoryItems.this, Categories.class);
                i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                startActivity(i);
            }
        });
        // --------------------------------------------------------------

        preferences = getSharedPreferences("default_preferences", MODE_PRIVATE);

        // AdMob Banner Ad initialization
        MobileAds.initialize(this, new OnInitializationCompleteListener() {
            @Override
            public void onInitializationComplete(InitializationStatus initializationStatus) { }
        });
        RequestConfiguration requestConfiguration = MobileAds.getRequestConfiguration()
                .toBuilder()
                .setTagForChildDirectedTreatment(TAG_FOR_CHILD_DIRECTED_TREATMENT_TRUE)
                .setMaxAdContentRating(MAX_AD_CONTENT_RATING_G)
                .build();
        MobileAds.setRequestConfiguration(requestConfiguration);
        mAdView = findViewById(R.id.adView);
        AdRequest adRequest = new AdRequest.Builder().build();
        mAdView.loadAd(adRequest);

        requestNewInterstitial();

        // In-App Review API - Enhanced Implementation
        simpleReviewManager = new SimpleReviewManager(this);
        simpleReviewManager.recordAppLaunch();

        // تهيئة UpdateManager للتحديثات
        updateManager = new UpdateManager(this);
        updateManager.checkForUpdates();

        // تهيئة AssetManager للمحتوى الديناميكي
        assetManager = new AssetManager(this);
        assetManager.checkInstalledPacks();

        Bundle extras = getIntent().getExtras();
        if (extras != null) {
            code = extras.getInt("code");
        }
        switch (code) {
            case 1:
                imagesArray = getResources().getIdentifier("Thumbnails1", "array", getPackageName());
                title = getString(R.string.flowers);
                break;
            case 2:
                imagesArray = getResources().getIdentifier("Thumbnails2", "array", getPackageName());
                title = getString(R.string.cartoons);
                break;
            case 3:
                imagesArray = getResources().getIdentifier("Thumbnails3", "array", getPackageName());
                title = getString(R.string.animals);
                break;
            case 4:
                imagesArray = getResources().getIdentifier("Thumbnails4", "array", getPackageName());
                title = getString(R.string.foods);
                break;
            case 5:
                imagesArray = getResources().getIdentifier("Thumbnails5", "array", getPackageName());
                title = getString(R.string.transport);
                break;
            case 6:
                imagesArray = getResources().getIdentifier("Thumbnails6", "array", getPackageName());
                title = getString(R.string.nature);
                break;
        }
        getSupportActionBar().setTitle(title);

        // Setup modern RecyclerView as primary display
        recyclerView = findViewById(R.id.recyclerView);
        if (recyclerView != null) {
            GridLayoutManager layoutManager = new GridLayoutManager(this, 2);
            layoutManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
                @Override
                public int getSpanSize(int position) {
                    return 1; // Each item takes 1 span
                }
            });
            recyclerView.setLayoutManager(layoutManager);
            recyclerView.setHasFixedSize(true);
            recyclerView.setItemAnimator(new androidx.recyclerview.widget.DefaultItemAnimator());

            modernAdapter = new ModernGridAdapter(this, getData(), code);
            recyclerView.setAdapter(modernAdapter);
            recyclerView.setVisibility(View.VISIBLE);
        }

        // Hide legacy GridView when RecyclerView is available
        gridView = (GridView) findViewById(R.id.gridView);
        if (recyclerView != null) {
            gridView.setVisibility(View.GONE);
        } else {
            // Fallback to legacy GridView only if RecyclerView is not available
            gridAdapter = new GridViewAdapter(this, R.layout.grid_item_layout, getData(), code);
            gridView.setAdapter(gridAdapter);
        }

        // onClick listener for grid items - Direct navigation without interstitial ads
        gridView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            public void onItemClick(AdapterView<?> parent, View v, int position, long id) {
                pos = position + 1;
                // Direct navigation to coloring activity without interstitial ads for better user experience
                Intent intent = new Intent(CategoryItems.this, MainActivityClean.class);
                intent.putExtra("position", pos);
                intent.putExtra("code", code);
                startActivity(intent);
            }
        });
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                // معالجة سهم الرجوع في الـ toolbar
                Intent i = new Intent(CategoryItems.this, Categories.class);
                i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                startActivity(i);
                return true;
            default:
                return super.onOptionsItemSelected(item);
        }
    }

    @Override
    public boolean onSupportNavigateUp() {
        // معالجة إضافية لسهم الرجوع
        Intent i = new Intent(CategoryItems.this, Categories.class);
        i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        startActivity(i);
        return true;
    }

    // Prepare data for the gridView based on the selected code
    private ArrayList<ImageItem> getData() {
        final ArrayList<ImageItem> imageItems = new ArrayList<>();
        TypedArray imgs = getResources().obtainTypedArray(imagesArray);
        for (int i = 0; i < imgs.length(); i++) {
            Bitmap bitmap = BitmapFactory.decodeResource(getResources(), imgs.getResourceId(i, -1));
            imageItems.add(new ImageItem(bitmap));
        }
        return imageItems;
    }

    private void requestNewInterstitial() {
        AdRequest adRequest = new AdRequest.Builder().build();
        InterstitialAd.load(
                this,
                getString(R.string.interstitial_unit_id),
                adRequest,
                new InterstitialAdLoadCallback() {

                    @Override
                    public void onAdLoaded(@NonNull InterstitialAd interstitialAd) {
                        mInterstitialAd = interstitialAd;
                        Log.i(TAG, "onAdLoaded");
                        interstitialAd.setFullScreenContentCallback(
                                new FullScreenContentCallback() {
                                    @Override
                                    public void onAdDismissedFullScreenContent() {
                                        mInterstitialAd = null;
                                        Log.d("TAG", "The ad was dismissed.");
                                        requestNewInterstitial();
                                        Intent intent = new Intent(CategoryItems.this, MainActivityClean.class);
                                        intent.putExtra("position", pos);
                                        intent.putExtra("code", code);
                                        startActivity(intent);
                                    }

                                    @Override
                                    public void onAdFailedToShowFullScreenContent(AdError adError) {
                                        mInterstitialAd = null;
                                        Log.d("TAG", "The ad failed to show.");
                                        requestNewInterstitial();
                                    }

                                    @Override
                                    public void onAdShowedFullScreenContent() {
                                        Log.d("TAG", "The ad was shown.");
                                        requestNewInterstitial();
                                    }
                                });
                    }

                    @Override
                    public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                        Log.i(TAG, loadAdError.getMessage());
                        mInterstitialAd = null;
                    }
                });
    }
}
