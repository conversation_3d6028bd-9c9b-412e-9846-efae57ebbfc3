<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:ads="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center_horizontal|center_vertical"
    android:gravity="center_horizontal|center_vertical"
    android:background="@color/divider"
    android:orientation="vertical">

    <GridView
        android:id="@+id/gridView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginLeft="5dp"
        android:layout_marginRight="5dp"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="10dp"
        android:drawSelectorOnTop="true"
        android:layout_gravity="center_horizontal|center_vertical"
        android:gravity="center_horizontal|center_vertical"
        android:columnWidth="250dp"
        android:numColumns="3"
        android:stretchMode="columnWidth"
        android:verticalSpacing="15dp"
        android:horizontalSpacing="15dp"
        android:focusable="true"
        android:clickable="true"
        />

    <!-- Bottom Ad Banner - Optimized Design -->
    <com.google.android.gms.ads.AdView
        xmlns:ads="http://schemas.android.com/apk/res-auto"
        android:id="@+id/adView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp"
        android:layout_marginBottom="8dp"
        ads:adSize="SMART_BANNER"
        ads:adUnitId="@string/banner_unit_id"/>
</LinearLayout>