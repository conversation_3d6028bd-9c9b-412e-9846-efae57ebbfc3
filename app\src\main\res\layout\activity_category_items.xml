<?xml version="1.0" encoding="utf-8"?>
<!-- Fixed Layout: Proper single toolbar system with CoordinatorLayout -->
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:ads="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    android:fitsSystemWindows="false">

    <!-- Compact App Bar - Single toolbar system -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="false"
        app:elevation="2dp">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/categoryToolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/surface_primary"
            android:minHeight="?attr/actionBarSize"
            app:titleTextColor="@color/on_surface_primary"
            app:navigationIconTint="@color/on_surface_primary" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Content with proper spacing -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:paddingBottom="100dp"
        android:clipToPadding="false"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            android:paddingTop="8dp"
            android:paddingBottom="8dp">

            <!-- Modern RecyclerView Grid -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:overScrollMode="never"
                android:scrollbars="none"
                tools:listitem="@layout/grid_item_layout" />

            <!-- Legacy GridView (hidden, for backward compatibility) -->
            <GridView
                android:id="@+id/gridView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:clipToPadding="false"
                android:columnWidth="160dp"
                android:drawSelectorOnTop="false"
                android:horizontalSpacing="12dp"
                android:numColumns="auto_fit"
                android:stretchMode="columnWidth"
                android:verticalSpacing="12dp" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Bottom Ad Banner - Clean Design -->
    <com.google.android.gms.ads.AdView
        android:id="@+id/adView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp"
        android:layout_marginBottom="78dp"
        android:layout_marginTop="8dp"
        ads:adSize="SMART_BANNER"
        ads:adUnitId="@string/banner_unit_id"/>

    <!-- Bottom Navigation -->
    <include
        layout="@layout/simple_bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>