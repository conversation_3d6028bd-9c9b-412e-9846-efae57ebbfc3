<?xml version="1.0" encoding="utf-8"?>
<!-- Fixed Layout: Removed duplicate toolbar system -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:ads="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_primary">

    <!-- Content with proper spacing - No duplicate toolbar -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true"
        android:paddingBottom="8dp"
        android:clipToPadding="false">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            android:paddingTop="8dp"
            android:paddingBottom="8dp">

            <!-- Modern RecyclerView Grid -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:overScrollMode="never"
                android:scrollbars="none"
                tools:listitem="@layout/grid_item_layout" />

            <!-- Legacy GridView (hidden, for backward compatibility) -->
            <GridView
                android:id="@+id/gridView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:clipToPadding="false"
                android:columnWidth="160dp"
                android:drawSelectorOnTop="false"
                android:horizontalSpacing="12dp"
                android:numColumns="auto_fit"
                android:stretchMode="columnWidth"
                android:verticalSpacing="12dp" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Bottom Ad Banner - Clean Design -->
    <com.google.android.gms.ads.AdView
        android:id="@+id/adView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp"
        android:layout_marginBottom="8dp"
        android:layout_marginTop="8dp"
        ads:adSize="SMART_BANNER"
        ads:adUnitId="@string/banner_unit_id"/>

</LinearLayout>